using Core.DataAccess;
using Core.Utilities.Results;
using Entities.Concrete;
using Entities.DTOs;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;


namespace DataAccess.Abstract
{
    public interface IMemberWorkoutProgramDal : IEntityRepository<MemberWorkoutProgram>
    {
        /// <summary>
        /// Şirket bazlı tüm program atamalarını getirir (admin paneli için)
        /// CompanyID otomatik olarak JWT token'dan alınır
        /// </summary>
        Task<List<MemberWorkoutProgramListDto>> GetCompanyAssignmentsAsync(CancellationToken ct = default);

        /// <summary>
        /// Belirli üyenin aktif programlarını getirir
        /// </summary>
        Task<List<MemberWorkoutProgramDto>> GetMemberActiveProgramsAsync(int memberId, CancellationToken ct = default);

        /// <summary>
        /// Belirli üyenin program geçmişini getirir
        /// </summary>
        Task<List<MemberWorkoutProgramHistoryDto>> GetMemberProgramHistoryAsync(int memberId, CancellationToken ct = default);

        /// <summary>
        /// User ID'ye göre aktif programları getirir (mobil API için)
        /// </summary>
        Task<List<MemberActiveWorkoutProgramDto>> GetActiveWorkoutProgramsByUserIdAsync(int userId, CancellationToken ct = default);

        /// <summary>
        /// Program atama detayını getirir
        /// </summary>
        Task<MemberWorkoutProgramDto> GetAssignmentDetailAsync(int assignmentId, CancellationToken ct = default);

        /// <summary>
        /// Belirli programa atanan üye sayısını getirir
        /// </summary>
        Task<int> GetAssignedMemberCountAsync(int workoutProgramTemplateId, CancellationToken ct = default);

        /// <summary>
        /// Şirket bazlı aktif atama sayısını getirir
        /// </summary>
        Task<int> GetActiveAssignmentCountAsync(int companyId, CancellationToken ct = default);

        /// <summary>
        /// Üyeye atanan program detayını getirir (mobil API için)
        /// </summary>
        Task<MemberWorkoutProgramDetailDto> GetProgramDetailByUserAsync(int userId, int memberWorkoutProgramId, CancellationToken ct = default);

        // SOLID prensiplerine uygun: Complex business logic DAL katmanında
        Task<IResult> AssignProgramWithValidationAsync(MemberWorkoutProgramAddDto assignmentDto, int companyId, CancellationToken ct = default);
        Task<IResult> UpdateAssignmentWithValidationAsync(MemberWorkoutProgramUpdateDto assignmentDto, int companyId, CancellationToken ct = default);
        Task<IResult> DeleteAssignmentWithValidationAsync(int assignmentId, int companyId, CancellationToken ct = default);
    }
}
